package com.pgs.dto.exception;

import jakarta.validation.Valid;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class ManualTransactionRequest {

    @NotBlank(message = "Bank account ID is required")
    private String bankAccountId;

    @NotBlank(message = "Transaction type is required")
    private String type; // "deposit" or "withdrawal"

    @NotNull(message = "Amount is required")
    @DecimalMin(value = "0.01", message = "Amount must be greater than 0")
    private BigDecimal amount;

    @NotNull(message = "Timestamp is required")
    private LocalDateTime timestamp;

    // Optional counterparty information
    @Valid
    private Counterparty counterparty;

    // Optional reference field
    private String reference;

    @Data
    public static class Counterparty {
        @NotBlank(message = "Account number is required")
        private String accountNumber;

        @NotBlank(message = "Bank name is required")
        private String bankName;
    }
}
