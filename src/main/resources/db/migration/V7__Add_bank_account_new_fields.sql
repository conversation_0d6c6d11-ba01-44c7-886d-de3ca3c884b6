-- Add new fields to bank_accounts table

-- Transaction Control Fields
ALTER TABLE bank_accounts 
ADD COLUMN enable_in BOOLEAN NOT NULL DEFAULT true,
ADD COLUMN enable_out BOOLEAN NOT NULL DEFAULT true;

-- Frozen Daily Usage Tracking Fields
ALTER TABLE bank_accounts 
ADD COLUMN freezed_daily_in_used DECIMAL(18,2) NOT NULL DEFAULT 0,
ADD COLUMN freezed_daily_out_used DECIMAL(18,2) NOT NULL DEFAULT 0;

-- Lifetime Total Tracking Fields
ALTER TABLE bank_accounts 
ADD COLUMN total_in DECIMAL(18,2) NOT NULL DEFAULT 0,
ADD COLUMN total_out DECIMAL(18,2) NOT NULL DEFAULT 0;

-- Add indexes for performance on the new boolean fields
CREATE INDEX idx_bank_accounts_enable_in ON bank_accounts(enable_in);
CREATE INDEX idx_bank_accounts_enable_out ON bank_accounts(enable_out);

-- Add comments for documentation
COMMENT ON COLUMN bank_accounts.enable_in IS 'Controls whether this bank account is currently allowed for deposit operations';
COMMENT ON COLUMN bank_accounts.enable_out IS 'Controls whether this bank account is currently allowed for withdrawal operations';
COMMENT ON COLUMN bank_accounts.freezed_daily_in_used IS 'Tracks pending deposit amounts that are frozen/reserved when deposit requests come in, before transaction matching is confirmed';
COMMENT ON COLUMN bank_accounts.freezed_daily_out_used IS 'Tracks pending withdrawal amounts that are frozen/reserved when withdrawal requests come in, before transaction matching is confirmed';
COMMENT ON COLUMN bank_accounts.total_in IS 'Cumulative total of all successful deposits since account creation';
COMMENT ON COLUMN bank_accounts.total_out IS 'Cumulative total of all successful withdrawals since account creation';
