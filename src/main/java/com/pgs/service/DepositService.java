package com.pgs.service;

import com.pgs.config.BusinessConfig;
import com.pgs.dto.deposit.DepositRequestDto;
import com.pgs.dto.deposit.DepositResponse;
import com.pgs.entity.BankAccount;
import com.pgs.entity.DepositRequest;
import com.pgs.entity.User;
import com.pgs.enums.DepositStatus;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.DepositRequestRepository;
import com.pgs.repository.UserRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class DepositService {

    private final DepositRequestRepository depositRequestRepository;
    private final BankAccountRepository bankAccountRepository;
    private final UserRepository userRepository;
    private final BusinessConfig businessConfig;
    private final AuditService auditService;

    /**
     * Creates a new deposit request and reserves the amount in the assigned bank account.
     * The frozen amount will be automatically released by RequestExpirationScheduler if the request expires.
     */
    @Transactional
    public DepositResponse createDepositRequest(DepositRequestDto request) {
        // Validate user exists
        //todo: no need to validate here, the userId in DepositRequestDto is not the user from our system, it is the user for the merchant system
        User user = userRepository.findById(UUID.fromString(request.getUserId()))
            .orElseThrow(() -> new ResourceNotFoundException("User", "id", request.getUserId()));

        // Check if order number already exists
        if (depositRequestRepository.findByOrderNumber(request.getOrderNumber()).isPresent()) {
            throw new BadRequestException("Order number already exists");
        }

        // Find available bank account
        List<BankAccount> availableAccounts = bankAccountRepository.findAvailableForDeposit(request.getAmount());
        if (availableAccounts.isEmpty()) {
            throw new BadRequestException("No bank account available for this deposit amount");
        }

        // Select the first available account (could implement more sophisticated selection logic)
        BankAccount selectedAccount = availableAccounts.get(0);

        // Create deposit request
        DepositRequest depositRequest = DepositRequest.builder()
            .user(user)
            .currency(request.getCurrency())
            .merchantCode(request.getMerchantCode())
            .orderNumber(request.getOrderNumber())
            .extendParam(request.getExtendParam())
            .amount(request.getAmount())
            .status(DepositStatus.PENDING)
            .assignedBankAccount(selectedAccount)
            .expiresAt(LocalDateTime.now().plusMinutes(businessConfig.getDeposit().getMatchingTimeoutMinutes()))
            .build();

        DepositRequest savedRequest = depositRequestRepository.save(depositRequest);

        // Update bank account frozen daily usage (reserve the amount)
        // Note: RequestExpirationScheduler runs every minute to check for expired requests and mark them as unpaid
        selectedAccount.freezeDepositAmount(request.getAmount());
        bankAccountRepository.save(selectedAccount);

        // todo: user of this action should be main system
        auditService.logUserAction(user.getId(), "DEPOSIT_REQUEST_CREATED", "DEPOSIT_REQUEST", 
            savedRequest.getId(), "Deposit request created: " + request.getOrderNumber());

        log.info("Deposit request created: {} for amount: {} assigned to bank: {}", 
            request.getOrderNumber(), request.getAmount(), selectedAccount.getBankName());

        return mapToDepositResponse(savedRequest);
    }

    private DepositResponse mapToDepositResponse(DepositRequest request) {
        return DepositResponse.builder()
            .requestId(request.getId())
            .orderNumber(request.getOrderNumber())
            .amount(request.getAmount())
            .currency(request.getCurrency())
            .status(request.getStatus().name())
            .bankName(request.getAssignedBankAccount().getBankName())
            .bankAccountNumber(request.getAssignedBankAccount().getAccountNumber())
            .accountHolderName(request.getAssignedBankAccount().getHolderName())
            .expiresAt(request.getExpiresAt())
            .createdAt(request.getCreatedAt())
            .build();
    }
}
