spring:
  datasource:
    url: jdbc:h2:mem:testdb
    driver-class-name: org.h2.Driver
    username: sa
    password: 
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    database-platform: org.hibernate.dialect.H2Dialect
  
  flyway:
    enabled: false

logging:
  level:
    com.pgs: DEBUG
    org.springframework.security: WARN

pgs:
  security:
    jwt:
      secret: test-secret-key-for-testing-only
      access-token-expiration: 3600000
      refresh-token-expiration: 86400000
    api:
      key: test-api-key
      allowed-ips: 127.0.0.1,::1
    admin:
      username: admin
      password: admin123
      email: <EMAIL>
  
  business:
    deposit:
      matching-timeout-minutes: 10
    withdrawal:
      expiry-timeout-minutes: 1
    daily-limit:
      reset-hour: 0
      timezone: Asia/Bangkok

  scheduler:
    pool-size: 2
    thread-name-prefix: Test-Scheduler-
    wait-for-tasks-to-complete-on-shutdown: true
    await-termination-seconds: 10
