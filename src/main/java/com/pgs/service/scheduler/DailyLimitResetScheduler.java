package com.pgs.service.scheduler;

import com.pgs.entity.BankAccount;
import com.pgs.repository.BankAccountRepository;
import com.pgs.service.AuditService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * Scheduled service to reset daily limits for all active bank accounts at midnight.
 * This ensures that daily usage tracking is properly reset without manual intervention.
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class DailyLimitResetScheduler {

    private final BankAccountRepository bankAccountRepository;
    private final AuditService auditService;

    /**
     * Scheduled job that runs at midnight (00:00) every day to reset daily limits.
     * Uses cron expression "0 0 0 * * *" for midnight execution.
     */
    @Scheduled(cron = "0 0 0 * * *")
    @Transactional
    public void resetDailyLimits() {
        log.info("Starting daily limit reset job at midnight");

        try {
            List<BankAccount> activeAccounts = bankAccountRepository.findAllActive();

            for (BankAccount account : activeAccounts) {
                account.resetDailyLimits();
            }

            bankAccountRepository.saveAll(activeAccounts);
            log.info("Daily limit reset completed for {} bank accounts", activeAccounts.size());

            // Log the operation for audit purposes
            auditService.logUserAction(null, "DAILY_LIMIT_RESET", "SYSTEM", null,
                "Daily limit reset completed for " + activeAccounts.size() + " bank accounts");

        } catch (Exception e) {
            log.error("Error occurred during daily limit reset job", e);

            // Log the error for audit purposes
            auditService.logUserAction(null, "DAILY_LIMIT_RESET_ERROR", "SYSTEM", null,
                "Daily limit reset job failed: " + e.getMessage());

            throw e;
        }
    }

}
