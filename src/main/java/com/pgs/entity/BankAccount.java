package com.pgs.entity;

import com.pgs.enums.AccountType;
import jakarta.persistence.*;
import lombok.*;

import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;

@Entity
@Table(name = "bank_accounts")
@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class BankAccount extends BaseEntity {

    @Column(nullable = false, length = 100)
    private String nickname;

    @Column(name = "account_number", unique = true, nullable = false, length = 64)
    private String accountNumber;

    @Column(name = "holder_name", nullable = false, length = 100)
    private String holderName;

    @Enumerated(EnumType.STRING)
    @Column(name = "account_type", nullable = false, length = 20)
    private AccountType accountType;

    @Column(name = "bank_name", nullable = false, length = 100)
    private String bankName;

    @Column(name = "in_transfer_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal inTransferLimit = BigDecimal.ZERO;

    @Column(name = "out_transfer_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal outTransferLimit = BigDecimal.ZERO;

    @Column(name = "balance_limit", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal balanceLimit = BigDecimal.ZERO;

    @Column(name = "current_balance", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal currentBalance = BigDecimal.ZERO;

    @Column(name = "daily_in_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal dailyInUsed = BigDecimal.ZERO;

    @Column(name = "daily_out_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal dailyOutUsed = BigDecimal.ZERO;

    @Column(name = "freezed_daily_in_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal freezedDailyInUsed = BigDecimal.ZERO;

    @Column(name = "freezed_daily_out_used", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal freezedDailyOutUsed = BigDecimal.ZERO;

    @Column(name = "total_in", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal totalIn = BigDecimal.ZERO;

    @Column(name = "total_out", nullable = false, precision = 18, scale = 2)
    @Builder.Default
    private BigDecimal totalOut = BigDecimal.ZERO;

    @Column(name = "enable_in", nullable = false)
    @Builder.Default
    private Boolean enableIn = true;

    @Column(name = "enable_out", nullable = false)
    @Builder.Default
    private Boolean enableOut = true;

    @Column(name = "is_active")
    @Builder.Default
    private Boolean isActive = true;

    @OneToMany(mappedBy = "bankAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<BankTransaction> transactions = new HashSet<>();

    @OneToMany(mappedBy = "bankAccount", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Builder.Default
    private Set<RobotStatusPeriod> robotStatusPeriods = new HashSet<>();

    public BigDecimal getAvailableInLimit() {
        return inTransferLimit.subtract(dailyInUsed).subtract(freezedDailyInUsed);
    }

    public BigDecimal getAvailableOutLimit() {
        return outTransferLimit.subtract(dailyOutUsed).subtract(freezedDailyOutUsed);
    }

    public boolean canAcceptDeposit(BigDecimal amount) {
        return isActive && enableIn &&
               getAvailableInLimit().compareTo(amount) >= 0 &&
               currentBalance.add(amount).compareTo(balanceLimit) <= 0;
    }

    public boolean canProcessWithdrawal(BigDecimal amount) {
        return isActive && enableOut &&
               getAvailableOutLimit().compareTo(amount) >= 0 &&
               currentBalance.compareTo(amount) >= 0;
    }

    public void resetDailyLimits() {
        dailyInUsed = BigDecimal.ZERO;
        dailyOutUsed = BigDecimal.ZERO;
        freezedDailyInUsed = BigDecimal.ZERO;
        freezedDailyOutUsed = BigDecimal.ZERO;
    }

    /**
     * Freezes the specified amount for deposit operations
     */
    public void freezeDepositAmount(BigDecimal amount) {
        this.freezedDailyInUsed = this.freezedDailyInUsed.add(amount);
    }

    /**
     * Freezes the specified amount for withdrawal operations
     */
    public void freezeWithdrawalAmount(BigDecimal amount) {
        this.freezedDailyOutUsed = this.freezedDailyOutUsed.add(amount);
    }

    /**
     * Releases frozen deposit amount (e.g., when request expires or is cancelled)
     */
    public void releaseFrozenDepositAmount(BigDecimal amount) {
        this.freezedDailyInUsed = this.freezedDailyInUsed.subtract(amount);
    }

    /**
     * Releases frozen withdrawal amount (e.g., when request expires or is cancelled)
     */
    public void releaseFrozenWithdrawalAmount(BigDecimal amount) {
        this.freezedDailyOutUsed = this.freezedDailyOutUsed.subtract(amount);
    }
}
