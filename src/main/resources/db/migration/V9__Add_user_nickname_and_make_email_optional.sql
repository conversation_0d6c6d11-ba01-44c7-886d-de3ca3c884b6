-- Add nickname column to users table and remove email column
ALTER TABLE users
ADD COLUMN nickname VARCHAR(100) NOT NULL DEFAULT 'User';

-- Update existing users to have a default nickname based on username
UPDATE users
SET nickname = COALESCE(NULLIF(username, ''), 'User')
WHERE nickname = 'User';

-- Remove the default value from nickname column after updating existing records
ALTER TABLE users
ALTER COLUMN nickname DROP DEFAULT;

-- Remove unique constraint from email column
ALTER TABLE users
DROP CONSTRAINT IF EXISTS users_email_key;

-- Drop the email column entirely
ALTER TABLE users
DROP COLUMN IF EXISTS email;
