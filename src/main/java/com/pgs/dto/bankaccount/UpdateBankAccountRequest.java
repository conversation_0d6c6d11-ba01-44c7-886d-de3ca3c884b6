package com.pgs.dto.bankaccount;

import com.pgs.enums.AccountType;
import jakarta.validation.constraints.DecimalMin;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Data
public class UpdateBankAccountRequest {

    @Size(max = 100, message = "Nickname must not exceed 100 characters")
    private String nickname;

    @DecimalMin(value = "0.0", message = "In-transfer limit must be non-negative")
    private BigDecimal inTransferLimit;

    @DecimalMin(value = "0.0", message = "Out-transfer limit must be non-negative")
    private BigDecimal outTransferLimit;

    @DecimalMin(value = "0.0", message = "Balance limit must be non-negative")
    private BigDecimal balanceLimit;

    @Size(max = 100, message = "Account holder name must not exceed 100 characters")
    private String accountHolderName;

    private AccountType accountType;

    private Boolean enableIn;

    private Boolean enableOut;
}
