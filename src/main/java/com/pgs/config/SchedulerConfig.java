package com.pgs.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.TaskScheduler;
import org.springframework.scheduling.concurrent.ThreadPoolTaskScheduler;

/**
 * Configuration for scheduled tasks in the Payment Gateway System.
 * Provides configuration for scheduler thread pool and task settings.
 */
@Configuration
@ConfigurationProperties(prefix = "pgs.scheduler")
@Data
public class SchedulerConfig {
    
    private int poolSize = 5;
    private String threadNamePrefix = "PGS-Scheduler-";
    private boolean waitForTasksToCompleteOnShutdown = true;
    private int awaitTerminationSeconds = 30;
    
    /**
     * Creates a custom TaskScheduler for scheduled jobs.
     * This provides better control over thread pool settings and error handling.
     */
    @Bean
    public TaskScheduler taskScheduler() {
        ThreadPoolTaskScheduler scheduler = new ThreadPoolTaskScheduler();
        scheduler.setPoolSize(poolSize);
        scheduler.setThreadNamePrefix(threadNamePrefix);
        scheduler.setWaitForTasksToCompleteOnShutdown(waitForTasksToCompleteOnShutdown);
        scheduler.setAwaitTerminationSeconds(awaitTerminationSeconds);
        scheduler.initialize();
        return scheduler;
    }
}
