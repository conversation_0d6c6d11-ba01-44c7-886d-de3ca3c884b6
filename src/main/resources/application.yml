server:
  port: ${SERVER_PORT:8080}
  servlet:
    context-path: /api/v1
  ssl:
    enabled: ${SSL_ENABLED:false}
    key-store: ${SSL_KEYSTORE_PATH:}
    key-store-password: ${SSL_KEYSTORE_PASSWORD:}
    key-store-type: ${SSL_KEYSTORE_TYPE:PKCS12}

spring:
  application:
    name: payment-gateway-system
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5432}/${DB_NAME:pgs_db}
    username: ${DB_USERNAME:pgs_user}
    password: ${DB_PASSWORD:pgs_password}
    driver-class-name: org.postgresql.Driver
    hikari:
      maximum-pool-size: ${DB_POOL_SIZE:20}
      minimum-idle: ${DB_POOL_MIN_IDLE:5}
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      leak-detection-threshold: 60000

  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: ${JPA_SHOW_SQL:false}
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
        use_sql_comments: true
        jdbc:
          batch_size: 20
        order_inserts: true
        order_updates: true

  flyway:
    enabled: true
    locations: classpath:db/migration
    baseline-on-migrate: true
    validate-on-migrate: true

  cache:
    type: caffeine
    caffeine:
      spec: maximumSize=1000,expireAfterWrite=1h

  security:
    require-ssl: ${REQUIRE_SSL:false}

logging:
  level:
    com.pgs: ${LOG_LEVEL:INFO}
    org.springframework.security: ${SECURITY_LOG_LEVEL:WARN}
    org.hibernate.SQL: ${SQL_LOG_LEVEL:WARN}
    org.hibernate.type.descriptor.sql.BasicBinder: ${SQL_PARAM_LOG_LEVEL:WARN}
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level [%X{traceId:-},%X{spanId:-}] %logger{36} - %msg%n"
  file:
    name: ${LOG_FILE_PATH:logs/pgs.log}

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
      base-path: /actuator
  endpoint:
    health:
      show-details: when-authorized
      show-components: when-authorized
  metrics:
    export:
      prometheus:
        enabled: true
  health:
    db:
      enabled: true

pgs:
  security:
    jwt:
      secret: ${JWT_SECRET:your-256-bit-secret-key-here-change-in-production}
      access-token-expiration: ${JWT_ACCESS_EXPIRATION:3600000} # 1 hour in milliseconds
      refresh-token-expiration: ${JWT_REFRESH_EXPIRATION:86400000} # 24 hours in milliseconds
    api:
      key: ${API_KEY:your-api-key-here}
      allowed-ips: ${ALLOWED_IPS:127.0.0.1,::1}
    admin:
      username: ${ADMIN_USERNAME:admin}
      password: ${ADMIN_PASSWORD:admin123}
      email: ${ADMIN_EMAIL:<EMAIL>}
  
  business:
    deposit:
      matching-timeout-minutes: ${DEPOSIT_MATCHING_TIMEOUT:10}
    withdrawal:
      expiry-timeout-minutes: ${WITHDRAWAL_EXPIRY_TIMEOUT:1}
    daily-limit:
      reset-hour: ${DAILY_LIMIT_RESET_HOUR:0} # Reset at midnight
      timezone: ${TIMEZONE:Asia/Bangkok}

  scheduler:
    pool-size: ${SCHEDULER_POOL_SIZE:5}
    thread-name-prefix: ${SCHEDULER_THREAD_PREFIX:PGS-Scheduler-}
    wait-for-tasks-to-complete-on-shutdown: ${SCHEDULER_WAIT_ON_SHUTDOWN:true}
    await-termination-seconds: ${SCHEDULER_TERMINATION_TIMEOUT:30}
  
  integration:
    main-system:
      base-url: ${MAIN_SYSTEM_BASE_URL:http://localhost:8081}
      timeout-seconds: ${MAIN_SYSTEM_TIMEOUT:30}
      retry-attempts: ${MAIN_SYSTEM_RETRY:3}
