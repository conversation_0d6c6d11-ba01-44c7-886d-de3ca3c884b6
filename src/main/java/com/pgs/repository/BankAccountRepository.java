package com.pgs.repository;

import com.pgs.entity.BankAccount;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.UUID;

@Repository
public interface BankAccountRepository extends JpaRepository<BankAccount, UUID> {
    
    Optional<BankAccount> findByAccountNumber(String accountNumber);
    
    boolean existsByAccountNumber(String accountNumber);
    
    @Query("SELECT ba FROM BankAccount ba WHERE ba.isActive = true")
    Page<BankAccount> findAllActive(Pageable pageable);
    
    @Query("SELECT ba FROM BankAccount ba WHERE ba.isActive = true")
    List<BankAccount> findAllActive();

    @Query("SELECT ba FROM BankAccount ba WHERE ba.isActive = true AND ba.enableIn = true AND " +
           "(ba.inTransferLimit - ba.dailyInUsed - ba.freezedDailyInUsed) >= :amount AND " +
           "(ba.currentBalance + :amount) <= ba.balanceLimit")
    List<BankAccount> findAvailableForDeposit(@Param("amount") BigDecimal amount);

    @Query("SELECT ba FROM BankAccount ba WHERE ba.isActive = true AND ba.enableOut = true AND " +
           "(ba.outTransferLimit - ba.dailyOutUsed - ba.freezedDailyOutUsed) >= :amount AND " +
           "ba.currentBalance >= :amount")
    List<BankAccount> findAvailableForWithdrawal(@Param("amount") BigDecimal amount);
    
    @Query("SELECT ba FROM BankAccount ba WHERE ba.bankName = :bankName AND ba.isActive = true")
    List<BankAccount> findByBankNameAndActive(@Param("bankName") String bankName);
}
