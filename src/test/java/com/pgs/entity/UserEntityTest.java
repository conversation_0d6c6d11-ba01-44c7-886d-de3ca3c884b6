package com.pgs.entity;

import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

class UserEntityTest {

    @Test
    void shouldCreateUserWithUsernameAndNickname() {
        // Given & When
        User user = User.builder()
            .username("testuser")
            .nickname("Test User")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // Then
        assertNotNull(user);
        assertEquals("testuser", user.getUsername());
        assertEquals("Test User", user.getNickname());
        assertEquals("hashedPassword", user.getPasswordHash());
        assertTrue(user.getIsActive());
    }

    @Test
    void shouldAllowNicknameUpdate() {
        // Given
        User user = User.builder()
            .username("testuser")
            .nickname("Original Nickname")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // When
        user.setNickname("Updated Nickname");

        // Then
        assertEquals("Updated Nickname", user.getNickname());
        assertEquals("testuser", user.getUsername()); // Username should remain unchanged
    }

    @Test
    void shouldNotHaveEmailField() {
        // Given
        User user = User.builder()
            .username("testuser")
            .nickname("Test User")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();

        // Then - This test verifies that email field doesn't exist
        // If email field existed, this test would fail at compilation
        assertNotNull(user);
        assertEquals("testuser", user.getUsername());
        assertEquals("Test User", user.getNickname());
    }
}
