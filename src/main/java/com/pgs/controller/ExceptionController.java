package com.pgs.controller;

import com.pgs.dto.deposit.DepositRequestDto;
import com.pgs.dto.exception.BalanceMismatchResponse;
import com.pgs.dto.exception.ManualTransactionRequest;
import com.pgs.dto.exception.MatchTransactionRequest;
import com.pgs.dto.withdrawal.WithdrawalRequestDto;
import com.pgs.service.ExceptionHandlingService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/frontend/exceptions")
@RequiredArgsConstructor
public class ExceptionController {

    private final ExceptionHandlingService exceptionHandlingService;

    @GetMapping("/unpaid-requests")
    @PreAuthorize("hasAuthority('EXCEPTION_READ')")
    public ResponseEntity<Page<Object>> getUnpaidRequests(
            @RequestParam(required = false) String bank,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> unpaidRequests = exceptionHandlingService.getUnpaidRequests(bank, date, pageable);
        
        return ResponseEntity.ok(unpaidRequests);
    }

    @GetMapping("/unmatched-transactions")
    @PreAuthorize("hasAuthority('EXCEPTION_READ')")
    public ResponseEntity<Page<Object>> getUnmatchedTransactions(
            @RequestParam(required = false) String bank,
            @RequestParam(required = false) @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate date,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Object> unmatchedTransactions = exceptionHandlingService.getUnmatchedTransactions(bank, date, pageable);
        
        return ResponseEntity.ok(unmatchedTransactions);
    }

    @GetMapping("/balance-mismatch")
    @PreAuthorize("hasAuthority('EXCEPTION_READ')")
    public ResponseEntity<List<BalanceMismatchResponse>> getBalanceMismatches() {
        List<BalanceMismatchResponse> mismatches = exceptionHandlingService.getBalanceMismatches();
        return ResponseEntity.ok(mismatches);
    }

    @PostMapping("/match-deposit")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    public ResponseEntity<Map<String, String>> matchDepositTransactionToRequest(
            @Valid @RequestBody MatchTransactionRequest request) {

        exceptionHandlingService.matchDepositTransactionToRequest(request);
        return ResponseEntity.ok(Map.of("message", "Deposit transaction matched successfully"));
    }

    @PostMapping("/match-withdrawal")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    public ResponseEntity<Map<String, String>> matchWithdrawalTransactionToRequest(
            @Valid @RequestBody MatchTransactionRequest request) {

        exceptionHandlingService.matchWithdrawalTransactionToRequest(request);
        return ResponseEntity.ok(Map.of("message", "Withdrawal transaction matched successfully"));
    }

    @PostMapping("/manual-transaction")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    public ResponseEntity<Map<String, String>> createManualTransaction(
            @Valid @RequestBody ManualTransactionRequest request) {
        
        exceptionHandlingService.createManualTransaction(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(Map.of("message", "Manual transaction created successfully"));
    }

    @PostMapping("/manual-deposit")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    public ResponseEntity<Map<String, String>> createManualDepositRequest(
            @Valid @RequestBody DepositRequestDto request) {
        
        exceptionHandlingService.createManualDepositRequest(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(Map.of("message", "Manual deposit request created successfully"));
    }

    @PostMapping("/manual-withdrawal")
    @PreAuthorize("hasAuthority('EXCEPTION_RESOLVE')")
    public ResponseEntity<Map<String, String>> createManualWithdrawalRequest(
            @Valid @RequestBody WithdrawalRequestDto request) {
        
        exceptionHandlingService.createManualWithdrawalRequest(request);
        return ResponseEntity.status(HttpStatus.CREATED)
            .body(Map.of("message", "Manual withdrawal request created successfully"));
    }
}
