package com.pgs.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.pgs.dto.exception.MatchTransactionRequest;
import com.pgs.service.ExceptionHandlingService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;

import java.util.UUID;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.springframework.security.test.web.servlet.request.SecurityMockMvcRequestPostProcessors.csrf;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.post;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

@WebMvcTest(ExceptionController.class)
class ExceptionControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private ExceptionHandlingService exceptionHandlingService;

    @Autowired
    private ObjectMapper objectMapper;

    @Test
    @WithMockUser(authorities = "EXCEPTION_RESOLVE")
    void testMatchDepositTransactionToRequest() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        doNothing().when(exceptionHandlingService).matchDepositTransactionToRequest(any(MatchTransactionRequest.class));

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-deposit")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Deposit transaction matched successfully"));

        verify(exceptionHandlingService).matchDepositTransactionToRequest(any(MatchTransactionRequest.class));
    }

    @Test
    @WithMockUser(authorities = "EXCEPTION_RESOLVE")
    void testMatchWithdrawalTransactionToRequest() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        doNothing().when(exceptionHandlingService).matchWithdrawalTransactionToRequest(any(MatchTransactionRequest.class));

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-withdrawal")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isOk())
                .andExpect(content().contentType(MediaType.APPLICATION_JSON))
                .andExpect(jsonPath("$.message").value("Withdrawal transaction matched successfully"));

        verify(exceptionHandlingService).matchWithdrawalTransactionToRequest(any(MatchTransactionRequest.class));
    }

    @Test
    @WithMockUser(authorities = "WRONG_AUTHORITY")
    void testMatchDepositTransactionToRequest_Forbidden() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-deposit")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    @WithMockUser(authorities = "WRONG_AUTHORITY")
    void testMatchWithdrawalTransactionToRequest_Forbidden() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-withdrawal")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isForbidden());
    }

    @Test
    void testMatchDepositTransactionToRequest_Unauthorized() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-deposit")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }

    @Test
    void testMatchWithdrawalTransactionToRequest_Unauthorized() throws Exception {
        // Arrange
        MatchTransactionRequest request = new MatchTransactionRequest();
        request.setTransactionId(UUID.randomUUID().toString());
        request.setRequestId(UUID.randomUUID().toString());

        // Act & Assert
        mockMvc.perform(post("/frontend/exceptions/match-withdrawal")
                .with(csrf())
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request)))
                .andExpect(status().isUnauthorized());
    }
}
