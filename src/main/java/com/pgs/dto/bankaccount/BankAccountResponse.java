package com.pgs.dto.bankaccount;

import com.pgs.enums.AccountType;
import lombok.Builder;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.UUID;

@Data
@Builder
public class BankAccountResponse {
    private UUID accountId;
    private String nickname;
    private String accountNumber;
    private String bankName;
    private BigDecimal inTransferLimit;
    private BigDecimal outTransferLimit;
    private BigDecimal balanceLimit;
    private String accountHolderName;
    private AccountType accountType;
    private BigDecimal currentBalance;
    private BigDecimal dailyInUsed;
    private BigDecimal dailyOutUsed;
    private BigDecimal freezedDailyInUsed;
    private BigDecimal freezedDailyOutUsed;
    private BigDecimal totalIn;
    private BigDecimal totalOut;
    private BigDecimal availableInLimit;
    private BigDecimal availableOutLimit;
    private Boolean enableIn;
    private Boolean enableOut;
    private Boolean isActive;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
