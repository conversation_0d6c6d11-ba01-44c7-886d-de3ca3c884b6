package com.pgs.service;

import com.pgs.dto.deposit.DepositRequestDto;
import com.pgs.dto.exception.BalanceMismatchResponse;
import com.pgs.dto.exception.ManualTransactionRequest;
import com.pgs.dto.exception.MatchTransactionRequest;
import com.pgs.dto.withdrawal.WithdrawalRequestDto;
import com.pgs.entity.*;
import com.pgs.enums.DepositStatus;
import com.pgs.enums.TransactionDirection;
import com.pgs.enums.TransactionStatus;
import com.pgs.enums.WithdrawalStatus;
import com.pgs.exception.BadRequestException;
import com.pgs.exception.ResourceNotFoundException;
import com.pgs.repository.*;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Service
@RequiredArgsConstructor
@Slf4j
public class ExceptionHandlingService {

    private final DepositRequestRepository depositRequestRepository;
    private final WithdrawalRequestRepository withdrawalRequestRepository;
    private final BankTransactionRepository bankTransactionRepository;
    private final BankAccountRepository bankAccountRepository;
    private final UserRepository userRepository;
    private final AuditService auditService;
    private final DepositService depositService;
    private final WithdrawalService withdrawalService;

    @Transactional(readOnly = true)
    public Page<Object> getUnpaidRequests(String bank, LocalDate date, Pageable pageable) {
        LocalDateTime dateTime = date != null ? date.atStartOfDay() : null;

        //todo: add audit log who viewed the report
        return depositRequestRepository.findUnpaidRequests(bank, dateTime, pageable)
            .map(this::mapDepositRequestToResponse);
    }

    @Transactional(readOnly = true)
    public Page<Object> getUnmatchedTransactions(String bank, LocalDate date, Pageable pageable) {
        LocalDateTime dateTime = date != null ? date.atStartOfDay() : null;

        //todo: add audit log who viewed the report
        return bankTransactionRepository.findUnmatchedTransactions(bank, dateTime, pageable)
            .map(this::mapBankTransactionToResponse);
    }

    @Transactional(readOnly = true)
    public List<BalanceMismatchResponse> getBalanceMismatches() {
        List<BankAccount> allAccounts = bankAccountRepository.findAllActive();
        List<BalanceMismatchResponse> mismatches = new ArrayList<>();

        // todo: there should be a table storing BankBalanceMismatch,
        //  robot system will send balance back from time to time, if there is a mismatch,
        //  an exception should be created, and if the mismatch is resolved, the exception should be closed
        //  this api should return all unresolved exceptions from the table

        for (BankAccount account : allAccounts) {
            // In a real implementation, you would fetch the actual balance from the bank robot
            // For now, we'll simulate by checking if system balance differs from a mock actual balance
            // This is where you'd integrate with the robot system to get real bank balances
            
            // Mock implementation - in reality this would come from robot balance fetch
            // For demonstration, we'll assume some accounts have mismatches
            if (account.getCurrentBalance().compareTo(account.getCurrentBalance()) != 0) {
                mismatches.add(BalanceMismatchResponse.builder()
                    .bankName(account.getBankName())
                    .bankAccount(account.getAccountNumber())
                    .bankNickname(account.getNickname())
                    .accountHolderName(account.getHolderName())
                    .accountType(account.getAccountType())
                    .systemBalance(account.getCurrentBalance())
                    .actualBalance(account.getCurrentBalance()) // This would be from robot
                    .exceptionTime(LocalDateTime.now())
                    .build());
            }
        }

        return mismatches;
    }

    @Transactional
    public void matchDepositTransactionToRequest(MatchTransactionRequest request) {
        // Find the transaction
        BankTransaction transaction = bankTransactionRepository.findById(UUID.fromString(request.getTransactionId()))
            .orElseThrow(() -> new ResourceNotFoundException("Transaction", "id", request.getTransactionId()));

        // Find the deposit request
        DepositRequest depositRequest = depositRequestRepository.findById(UUID.fromString(request.getRequestId()))
            .orElseThrow(() -> new ResourceNotFoundException("Deposit Request", "id", request.getRequestId()));

        // Validate the match is possible
        if (transaction.getMatchedRequestId() != null) {
            throw new BadRequestException("Transaction is already matched");
        }

        if (!DepositStatus.PENDING.equals(depositRequest.getStatus()) &&
            !DepositStatus.UNPAID.equals(depositRequest.getStatus())) {
            throw new BadRequestException("Deposit request cannot be matched in current status");
        }

        if (!transaction.isIncoming()) {
            throw new BadRequestException("Only incoming transactions can be matched to deposit requests");
        }

        if (DepositStatus.PENDING.equals(depositRequest.getStatus())) {
            BankAccount bankAccount = depositRequest.getAssignedBankAccount();
            if (bankAccount != null) {
                bankAccount.releaseFrozenDepositAmount(depositRequest.getAmount());
                bankAccountRepository.save(bankAccount);
            }
        }

        // Perform the match
        transaction.setMatchedRequestId(depositRequest.getId());
        transaction.setStatus(TransactionStatus.COMPLETED); //todo: set status to MATCHED

        depositRequest.setMatchedTransaction(transaction);
        depositRequest.setStatus(DepositStatus.MATCHED);

        bankTransactionRepository.save(transaction);
        depositRequestRepository.save(depositRequest);

        // todo: log who matched the transaction
        auditService.logUserAction(null, "MANUAL_MATCH", "TRANSACTION", transaction.getId(),
            "Manually matched transaction " + transaction.getId() + " to deposit request " + depositRequest.getId());

        log.info("Manually matched transaction {} to deposit request {}", 
            transaction.getId(), depositRequest.getId());

        // todo: send callback to main system
    }

    @Transactional
    public void matchWithdrawalTransactionToRequest(MatchTransactionRequest request) {
        // Find the transaction
        BankTransaction transaction = bankTransactionRepository.findById(UUID.fromString(request.getTransactionId()))
            .orElseThrow(() -> new ResourceNotFoundException("Transaction", "id", request.getTransactionId()));

        // Find the withdrawal request
        WithdrawalRequest withdrawalRequest = withdrawalRequestRepository.findById(UUID.fromString(request.getRequestId()))
            .orElseThrow(() -> new ResourceNotFoundException("Withdrawal Request", "id", request.getRequestId()));

        // Validate the match is possible
        if (transaction.getMatchedRequestId() != null) {
            throw new BadRequestException("Transaction is already matched");
        }

        if (!WithdrawalStatus.PENDING.equals(withdrawalRequest.getStatus()) &&
            !WithdrawalStatus.PROCESSING.equals(withdrawalRequest.getStatus())) {
            throw new BadRequestException("Withdrawal request cannot be matched in current status");
        }

        if (transaction.isIncoming()) {
            throw new BadRequestException("Only outgoing transactions can be matched to withdrawal requests");
        }

        // Perform the match
        transaction.setMatchedRequestId(withdrawalRequest.getId());
        transaction.setStatus(TransactionStatus.COMPLETED); //todo: set status to MATCHED

        withdrawalRequest.setTransaction(transaction);
        withdrawalRequest.setStatus(WithdrawalStatus.SUCCESS);

        if (WithdrawalStatus.PENDING.equals(withdrawalRequest.getStatus())) {
            BankAccount bankAccount = withdrawalRequest.getBankAccount();
            if (bankAccount != null) {
                bankAccount.releaseFrozenWithdrawalAmount(withdrawalRequest.getAmount());
                bankAccountRepository.save(bankAccount);
            }
        }

        bankTransactionRepository.save(transaction);
        withdrawalRequestRepository.save(withdrawalRequest);

        // todo: log who matched the transaction
        auditService.logUserAction(null, "MANUAL_MATCH", "TRANSACTION", transaction.getId(),
            "Manually matched transaction " + transaction.getId() + " to withdrawal request " + withdrawalRequest.getId());

        log.info("Manually matched transaction {} to withdrawal request {}",
            transaction.getId(), withdrawalRequest.getId());

        // todo: send callback to main system
    }

    @Transactional
    public void createManualTransaction(ManualTransactionRequest request) {
        BankAccount bankAccount = bankAccountRepository.findById(UUID.fromString(request.getBankAccountId()))
            .orElseThrow(() -> new ResourceNotFoundException("Bank Account", "id", request.getBankAccountId()));

        TransactionDirection direction = "deposit".equalsIgnoreCase(request.getType()) ?
            TransactionDirection.IN : TransactionDirection.OUT;

        // Handle optional counterparty information
        String counterpartyAccount = null;
        String counterpartyBank = null;
        if (request.getCounterparty() != null) {
            counterpartyAccount = request.getCounterparty().getAccountNumber();
            counterpartyBank = request.getCounterparty().getBankName();
        }

        // Use provided reference or generate default one
        String reference = request.getReference() != null && !request.getReference().trim().isEmpty()
            ? request.getReference()
            : "MANUAL_" + System.currentTimeMillis();

        BankTransaction transaction = BankTransaction.builder()
            .bankAccount(bankAccount)
            .direction(direction)
            .amount(request.getAmount())
            .transactionTime(request.getTimestamp())
            .status(TransactionStatus.COMPLETED)
            .counterpartyAccount(counterpartyAccount)
            .counterpartyBank(counterpartyBank)
            .reference(reference)
            .build();

        bankTransactionRepository.save(transaction);

        // Update bank account balance and totals
        if (direction == TransactionDirection.IN) {
            bankAccount.setCurrentBalance(bankAccount.getCurrentBalance().add(request.getAmount()));
            bankAccount.setTotalIn(bankAccount.getTotalIn().add(request.getAmount()));
        } else {
            bankAccount.setCurrentBalance(bankAccount.getCurrentBalance().subtract(request.getAmount()));
            bankAccount.setTotalOut(bankAccount.getTotalOut().add(request.getAmount()));
        }
        bankAccountRepository.save(bankAccount);

        auditService.logUserAction(null, "MANUAL_TRANSACTION_CREATED", "TRANSACTION", transaction.getId(),
            "Manual transaction created: " + request.getType() + " of " + request.getAmount());

        log.info("Manual {} transaction created for amount {} on account {}", 
            request.getType(), request.getAmount(), bankAccount.getNickname());

        // todo: log who created the manual transaction
    }

    @Transactional
    public void createManualDepositRequest(DepositRequestDto request) {
        // Use the existing deposit service but mark it as manual
        depositService.createDepositRequest(request);

        // todo: log who created the manual request
        auditService.logUserAction(null, "MANUAL_DEPOSIT_REQUEST_CREATED", "DEPOSIT_REQUEST", null,
            "Manual deposit request created: " + request.getOrderNumber());
    }

    @Transactional
    public void createManualWithdrawalRequest(WithdrawalRequestDto request) {
        // Use the existing withdrawal service but mark it as manual
        withdrawalService.createWithdrawalRequest(request);

        // todo: log who created the manual request
        auditService.logUserAction(null, "MANUAL_WITHDRAWAL_REQUEST_CREATED", "WITHDRAWAL_REQUEST", null,
            "Manual withdrawal request created: " + request.getOrderNumber());
    }

    private Object mapDepositRequestToResponse(DepositRequest request) {
        // Return a simplified response object for unpaid requests
        return new Object() {
            public UUID getId() { return request.getId(); }
            public String getOrderNumber() { return request.getOrderNumber(); }
            public String getAmount() { return request.getAmount().toString(); }
            public String getStatus() { return request.getStatus().name(); }
            public String getBankName() { 
                return request.getAssignedBankAccount() != null ? 
                    request.getAssignedBankAccount().getBankName() : null; 
            }
            public LocalDateTime getCreatedAt() { return request.getCreatedAt(); }
            public LocalDateTime getExpiresAt() { return request.getExpiresAt(); }
        };
    }

    private Object mapBankTransactionToResponse(BankTransaction transaction) {
        // Return a simplified response object for unmatched transactions
        return new Object() {
            public UUID getId() { return transaction.getId(); }
            public String getAmount() { return transaction.getAmount().toString(); }
            public String getDirection() { return transaction.getDirection().name(); }
            public String getStatus() { return transaction.getStatus().name(); }
            public String getBankName() { return transaction.getBankAccount().getBankName(); }
            public LocalDateTime getTransactionTime() { return transaction.getTransactionTime(); }
            public String getReference() { return transaction.getReference(); }
            public String getCounterpartyAccount() { return transaction.getCounterpartyAccount(); }
            public String getCounterpartyBank() { return transaction.getCounterpartyBank(); }
        };
    }
}
