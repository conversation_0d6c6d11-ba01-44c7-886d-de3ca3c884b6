openapi: 3.0.0
info:
  title: Payment Gateway System (PGS) API
  description: API contract for the Payment Gateway System including Main System,
    Robot, and Frontend communications.
  version: 1.0.0
servers:
- url: https://api.pgs.example.com/v1
  description: Production server
paths:
  /frontend/users:
    post:
      tags:
      - users
      summary: Create User
      description: Creates a new user.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                password:
                  type: string
                email:
                  type: string
                roles:
                  type: array
                  items:
                    type: string
              required:
              - username
              - password
              - email
              - roles
      responses:
        '201':
          description: User created
          content:
            application/json:
              schema:
                type: object
                properties:
                  userId:
                    type: string
                  username:
                    type: string
                  email:
                    type: string
                  roles:
                    type: array
                    items:
                      type: string
    get:
      tags:
      - users
      summary: List Users
      description: Lists all users with pagination.
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        schema:
          type: integer
      - name: size
        in: query
        schema:
          type: integer
      responses:
        '200':
          description: List of users
          content:
            application/json:
              schema:
                type: object
                properties:
                  users:
                    type: array
                    items:
                      type: object
                      properties:
                        userId:
                          type: string
                        username:
                          type: string
                        email:
                          type: string
                  total:
                    type: integer
  /frontend/users/{userId}:
    get:
      tags:
      - users
      summary: Get User
      description: Retrieves a user by ID.
      security:
      - bearerAuth: []
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: User details
          content:
            application/json:
              schema:
                type: object
                properties:
                  userId:
                    type: string
                  username:
                    type: string
                  email:
                    type: string
                  roles:
                    type: array
                    items:
                      type: string
    put:
      tags:
      - users
      summary: Update User
      description: Updates a user's details.
      security:
      - bearerAuth: []
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                username:
                  type: string
                email:
                  type: string
                roles:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: User updated
    delete:
      tags:
      - users
      summary: Delete User
      description: Deletes a user.
      security:
      - bearerAuth: []
      parameters:
      - name: userId
        in: path
        required: true
        schema:
          type: string
      responses:
        '204':
          description: User deleted
  /frontend/roles:
    post:
      tags:
      - roles
      summary: Create Role
      description: Creates a new role.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleName:
                  type: string
                permissions:
                  type: array
                  items:
                    type: string
              required:
              - roleName
              - permissions
      responses:
        '201':
          description: Role created
          content:
            application/json:
              schema:
                type: object
                properties:
                  roleId:
                    type: string
                  roleName:
                    type: string
                  permissions:
                    type: array
                    items:
                      type: string
    get:
      tags:
      - roles
      summary: List Roles
      description: Lists all roles with pagination.
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        schema:
          type: integer
      - name: size
        in: query
        schema:
          type: integer
      responses:
        '200':
          description: List of roles
          content:
            application/json:
              schema:
                type: object
                properties:
                  roles:
                    type: array
                    items:
                      type: object
                      properties:
                        roleId:
                          type: string
                        roleName:
                          type: string
                  total:
                    type: integer
  /frontend/roles/{roleId}:
    get:
      tags:
      - roles
      summary: Get Role
      description: Retrieves a role by ID.
      security:
      - bearerAuth: []
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Role details
          content:
            application/json:
              schema:
                type: object
                properties:
                  roleId:
                    type: string
                  roleName:
                    type: string
                  permissions:
                    type: array
                    items:
                      type: string
    put:
      tags:
      - roles
      summary: Update Role
      description: Updates a role's details.
      security:
      - bearerAuth: []
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                roleName:
                  type: string
                permissions:
                  type: array
                  items:
                    type: string
      responses:
        '200':
          description: Role updated
    delete:
      tags:
      - roles
      summary: Delete Role
      description: Deletes a role.
      security:
      - bearerAuth: []
      parameters:
      - name: roleId
        in: path
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Role deleted
  /frontend/bank-accounts:
    post:
      tags:
      - Bank Accounts
      summary: Create Bank Account
      description: Creates a new bank account.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                accountNumber:
                  type: string
                bankName:
                  type: string
                inTransferLimit:
                  type: number
                  format: decimal
                outTransferLimit:
                  type: number
                  format: decimal
                balanceLimit:
                  type: number
                  format: decimal
                accountHolderName:
                  type: string
                  description: Account holder's name
                accountType:
                  type: string
                  enum:
                  - PERSONAL
                  - CORPORATE
                  description: Type of the account
              required:
              - nickname
              - accountNumber
              - bankName
              - inTransferLimit
              - outTransferLimit
              - balanceLimit
              - accountHolderName
              - accountType
      responses:
        '201':
          description: Bank account created
          content:
            application/json:
              schema:
                type: object
                properties:
                  accountId:
                    type: string
                  nickname:
                    type: string
                  accountNumber:
                    type: string
                  bankName:
                    type: string
    get:
      tags:
      - Bank Accounts
      summary: List Bank Accounts
      description: Lists all bank accounts with pagination.
      security:
      - bearerAuth: []
      parameters:
      - name: page
        in: query
        schema:
          type: integer
      - name: size
        in: query
        schema:
          type: integer
      responses:
        '200':
          description: List of bank accounts
          content:
            application/json:
              schema:
                type: object
                properties:
                  accounts:
                    type: array
                    items:
                      type: object
                      properties:
                        accountId:
                          type: string
                        nickname:
                          type: string
                        accountNumber:
                          type: string
                        bankName:
                          type: string
                        accountHolderName:
                          type: string
                          description: Account holder's name
                        accountType:
                          type: string
                          enum:
                          - PERSONAL
                          - CORPORATE
                          description: Type of the account
                  total:
                    type: integer
  /frontend/bank-accounts/{accountId}:
    get:
      tags:
      - Bank Accounts
      summary: Get Bank Account
      description: Retrieves a bank account by ID.
      security:
      - bearerAuth: []
      parameters:
      - name: accountId
        in: path
        required: true
        schema:
          type: string
      responses:
        '200':
          description: Bank account details
          content:
            application/json:
              schema:
                type: object
                properties:
                  accountId:
                    type: string
                  nickname:
                    type: string
                  accountNumber:
                    type: string
                  bankName:
                    type: string
                  inTransferLimit:
                    type: number
                    format: decimal
                  outTransferLimit:
                    type: number
                    format: decimal
                  balanceLimit:
                    type: number
                    format: decimal
                  accountHolderName:
                    type: string
                    description: Account holder's name
                  accountType:
                    type: string
                    enum:
                    - PERSONAL
                    - CORPORATE
                    description: Type of the account
    put:
      tags:
      - Bank Accounts
      summary: Update Bank Account
      description: Updates a bank account's details.
      security:
      - bearerAuth: []
      parameters:
      - name: accountId
        in: path
        required: true
        schema:
          type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                nickname:
                  type: string
                inTransferLimit:
                  type: number
                  format: decimal
                outTransferLimit:
                  type: number
                  format: decimal
                balanceLimit:
                  type: number
                  format: decimal
                accountHolderName:
                  type: string
                  description: Account holder's name
                accountType:
                  type: string
                  enum:
                  - PERSONAL
                  - CORPORATE
                  description: Type of the account
      responses:
        '200':
          description: Bank account updated
    delete:
      tags:
      - Bank Accounts
      summary: Delete Bank Account
      description: Deletes a bank account.
      security:
      - bearerAuth: []
      parameters:
      - name: accountId
        in: path
        required: true
        schema:
          type: string
      responses:
        '204':
          description: Bank account deleted
  /frontend/exceptions/unpaid-requests:
    get:
      tags:
      - exception-handling
      summary: List unpaid requests
      description: Retrieve unpaid deposit requests with filters.
      parameters:
      - name: bank
        in: query
        required: false
        schema:
          type: string
      - name: date
        in: query
        required: false
        schema:
          type: string
          format: date
      responses:
        '200':
          description: List of unpaid requests
  /frontend/exceptions/unmatched-transactions:
    get:
      tags:
      - exception-handling
      summary: List unmatched transactions
      description: Retrieve unmatched bank transactions with filters.
      parameters:
      - name: bank
        in: query
        required: false
        schema:
          type: string
      - name: date
        in: query
        required: false
        schema:
          type: string
          format: date
      responses:
        '200':
          description: List of unmatched transactions
  /frontend/exceptions/balance-mismatch:
    get:
      tags:
      - exception-handling
      summary: List current bank balance mismatches
      description: Returns the current list of banks where the system balance and
        actual bank balance are not equal. No input parameters required.
      responses:
        '200':
          description: List of banks with system and actual balances (only where they
            differ)
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    bankName:
                      type: string
                      description: Name of the bank
                    bankAccount:
                      type: string
                      description: Bank account number
                    bankNickname:
                      type: string
                      description: Nickname of the bank account
                    accountHolderName:
                      type: string
                      description: Account holder's name
                    accountType:
                      type: string
                      enum:
                      - PERSONAL
                      - CORPORATE
                      description: Type of the account
                    systemBalance:
                      type: number
                      format: float
                      description: Balance recorded in the system
                    actualBalance:
                      type: number
                      format: float
                      description: Balance fetched from bank
                    exceptionTime:
                      type: string
                      format: date-time
                      description: Timestamp when the mismatch was detected
  /frontend/exceptions/match:
    post:
      tags:
      - exception-handling
      summary: Match Transaction to Request
      description: Manually matches a transaction to a request.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                transactionId:
                  type: string
                requestId:
                  type: string
              required:
              - transactionId
              - requestId
      responses:
        '200':
          description: Match successful
  /frontend/exceptions/manual-transaction:
    post:
      tags:
      - exception-handling
      summary: Create Manual Bank Transaction
      description: Manually creates a bank transaction.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                bankAccountId:
                  type: string
                type:
                  type: string
                  enum:
                  - deposit
                  - withdrawal
                amount:
                  type: number
                  format: decimal
                timestamp:
                  type: string
                  format: date-time
                counterparty:
                  type: object
                  properties:
                    accountNumber:
                      type: string
                    bankName:
                      type: string
                reference:
                  type: string
                  description: Optional transaction reference
              required:
              - bankAccountId
              - type
              - amount
              - timestamp
      responses:
        '201':
          description: Transaction created
  /frontend/exceptions/manual-deposit:
    post:
      tags:
      - exception-handling
      summary: Create Manual Deposit Request
      description: Manually creates a deposit request.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  format: decimal
                userId:
                  type: string
                currency:
                  type: string
                merchantCode:
                  type: string
                orderNumber:
                  type: string
                extendParam:
                  type: string
              required:
              - amount
              - userId
              - currency
              - merchantCode
              - orderNumber
      responses:
        '201':
          description: Deposit request created (***remove for production***)
  /frontend/exceptions/manual-withdrawal:
    post:
      tags:
      - exception-handling
      summary: Create Manual Withdrawal Request
      description: Manually creates a withdrawal request.
      security:
      - bearerAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                amount:
                  type: number
                  format: decimal
                userId:
                  type: string
                targetAccount:
                  type: object
                  properties:
                    accountNumber:
                      type: string
                    bankName:
                      type: string
                currency:
                  type: string
                merchantCode:
                  type: string
                orderNumber:
                  type: string
                extendParam:
                  type: string
              required:
              - amount
              - userId
              - targetAccount
              - currency
              - merchantCode
              - orderNumber
      responses:
        '201':
          description: Withdrawal request created (***remove for production***)
  /frontend/reports/transactions:
    get:
      tags:
      - reporting
      summary: Search transaction reports
      description: Search by partial transactionId or orderNumber.
      parameters:
      - name: search
        in: query
        required: false
        schema:
          type: string
      - name: from
        in: query
        required: false
        schema:
          type: string
          format: date
      - name: to
        in: query
        required: false
        schema:
          type: string
          format: date
      responses:
        '200':
          description: Filtered transaction reports
  /frontend/reports/bank-balances:
    get:
      tags:
      - reports
      summary: Get Bank Balances
      description: Retrieves current balances of all bank accounts.
      security:
      - bearerAuth: []
      responses:
        '200':
          description: Bank balances
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    bankAccountId:
                      type: string
                    balance:
                      type: number
                      format: decimal
  /frontend/reports/success-rate:
    get:
      tags:
      - reporting
      summary: Get deposit and withdrawal success rates
      description: Returns list of success rates within the given date range.
      parameters:
      - name: from
        in: query
        required: true
        schema:
          type: string
          format: date
      - name: to
        in: query
        required: true
        schema:
          type: string
          format: date
      responses:
        '200':
          description: List of <date, success-rate>
  /audit/logs:
    get:
      tags:
      - audit-log
      summary: List audit logs
      description: Retrieve audit logs with optional filters.
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: string
      - name: type
        in: query
        required: false
        schema:
          type: string
      - name: from
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: to
        in: query
        required: false
        schema:
          type: string
          format: date-time
      responses:
        '200':
          description: Audit log list
  /audit/logs/export:
    post:
      tags:
      - audit-log
      summary: Export audit logs
      description: Export filtered audit logs as CSV.
      requestBody:
        required: false
        content:
          application/json:
            schema:
              type: object
              properties:
                userId:
                  type: string
                type:
                  type: string
                from:
                  type: string
                  format: date-time
                to:
                  type: string
                  format: date-time
      responses:
        '200':
          description: CSV file download link
      parameters:
      - name: userId
        in: query
        required: false
        schema:
          type: string
      - name: type
        in: query
        required: false
        schema:
          type: string
      - name: from
        in: query
        required: false
        schema:
          type: string
          format: date-time
      - name: to
        in: query
        required: false
        schema:
          type: string
          format: date-time
  /auth/2fa/setup:
    get:
      tags:
      - auth-2fa
      summary: Setup 2FA
      description: Generate QR code or key for 2FA setup.
      responses:
        '200':
          description: 2FA secret and QR code
  /auth/2fa/verify:
    post:
      tags:
      - auth-2fa
      summary: Verify 2FA code
      description: Verify the TOTP code entered by the user.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                code:
                  type: string
              required:
              - code
      responses:
        '200':
          description: 2FA verification result
  /frontend/reports/deposit-request:
    get:
      tags:
      - reporting
      summary: Deposit request report
      description: View deposit requests by date range.
      parameters:
      - name: from
        in: query
        required: false
        schema:
          type: string
          format: date
      - name: to
        in: query
        required: false
        schema:
          type: string
          format: date
      - name: bank
        in: query
        required: false
        schema:
          type: string
      responses:
        '200':
          description: List of deposit requests
  /frontend/reports/withdrawal-request:
    get:
      tags:
      - reporting
      summary: Withdrawal request report
      description: View withdrawal requests by date range.
      parameters:
      - name: from
        in: query
        required: false
        schema:
          type: string
          format: date
      - name: to
        in: query
        required: false
        schema:
          type: string
          format: date
      - name: bank
        in: query
        required: false
        schema:
          type: string
      responses:
        '200':
          description: List of withdrawal requests
components:
  securitySchemes:
    bearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
