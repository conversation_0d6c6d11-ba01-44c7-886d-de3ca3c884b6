-- Add nickname column to users table and make email optional
ALTER TABLE users 
ADD COLUMN nickname VARCHAR(100) NOT NULL DEFAULT 'User';

-- Remove NOT NULL constraint from email column
ALTER TABLE users 
ALTER COLUMN email DROP NOT NULL;

-- Remove unique constraint from email column temporarily
ALTER TABLE users 
DROP CONSTRAINT IF EXISTS users_email_key;

-- Add unique constraint back to email column but allow nulls
CREATE UNIQUE INDEX users_email_unique_idx ON users (email) WHERE email IS NOT NULL;

-- Update existing users to have a default nickname based on username
UPDATE users 
SET nickname = COALESCE(NULLIF(username, ''), 'User') 
WHERE nickname = 'User';

-- Remove the default value from nickname column after updating existing records
ALTER TABLE users 
ALTER COLUMN nickname DROP DEFAULT;
