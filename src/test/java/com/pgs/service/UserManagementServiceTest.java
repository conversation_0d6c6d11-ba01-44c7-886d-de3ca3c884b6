package com.pgs.service;

import com.pgs.dto.user.CreateUserRequest;
import com.pgs.dto.user.UserResponse;
import com.pgs.entity.Role;
import com.pgs.entity.User;
import com.pgs.exception.BadRequestException;
import com.pgs.repository.RoleRepository;
import com.pgs.repository.UserRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Set;
import java.util.UUID;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class UserManagementServiceTest {

    @Mock
    private UserRepository userRepository;

    @Mock
    private RoleRepository roleRepository;

    @Mock
    private PasswordEncoder passwordEncoder;

    @Mock
    private AuditService auditService;

    @Mock
    private TwoFactorAuthService twoFactorAuthService;

    @InjectMocks
    private UserManagementService userManagementService;

    private CreateUserRequest createUserRequest;
    private Role userRole;
    private User user;

    @BeforeEach
    void setUp() {
        createUserRequest = new CreateUserRequest();
        createUserRequest.setUsername("testuser");
        createUserRequest.setNickname("Test User");
        createUserRequest.setPassword("password123");
        createUserRequest.setRoles(Set.of("USER"));

        userRole = Role.builder()
            .name("USER")
            .build();

        user = User.builder()
            .username("testuser")
            .nickname("Test User")
            .passwordHash("hashedPassword")
            .isActive(true)
            .build();
    }

    @Test
    void shouldCreateUserSuccessfully() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(roleRepository.findByNameIn(any())).thenReturn(Set.of(userRole));
        when(passwordEncoder.encode(anyString())).thenReturn("hashedPassword");
        when(userRepository.save(any(User.class))).thenReturn(user);
        when(twoFactorAuthService.isTwoFactorEnabled(any(User.class))).thenReturn(false);

        // When
        UserResponse response = userManagementService.createUser(createUserRequest);

        // Then
        assertNotNull(response);
        assertEquals("testuser", response.getUsername());
        assertEquals("Test User", response.getNickname());
        assertTrue(response.getIsActive());
        assertFalse(response.getTwoFactorEnabled());

        verify(userRepository).save(any(User.class));
        verify(auditService).logUserAction(any(), eq("USER_CREATED"), eq("USER"), any(), anyString());
    }

    @Test
    void shouldThrowExceptionWhenUsernameExists() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(true);

        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class, 
            () -> userManagementService.createUser(createUserRequest));
        
        assertEquals("Username already exists", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }



    @Test
    void shouldThrowExceptionWhenRoleNotFound() {
        // Given
        when(userRepository.existsByUsername(anyString())).thenReturn(false);
        when(userRepository.existsByEmail(anyString())).thenReturn(false);
        when(roleRepository.findByNameIn(any())).thenReturn(Set.of()); // Empty set

        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class, 
            () -> userManagementService.createUser(createUserRequest));
        
        assertTrue(exception.getMessage().contains("Roles not found"));
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void shouldThrowExceptionWhenNicknameIsNull() {
        // Given
        createUserRequest.setNickname(null);

        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class,
            () -> userManagementService.createUser(createUserRequest));

        assertEquals("Nickname is required", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }

    @Test
    void shouldThrowExceptionWhenNicknameIsEmpty() {
        // Given
        createUserRequest.setNickname("   ");

        // When & Then
        BadRequestException exception = assertThrows(BadRequestException.class,
            () -> userManagementService.createUser(createUserRequest));

        assertEquals("Nickname is required", exception.getMessage());
        verify(userRepository, never()).save(any(User.class));
    }


}
