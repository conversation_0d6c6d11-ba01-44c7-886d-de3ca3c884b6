package com.pgs.service;

import com.pgs.entity.BankAccount;
import com.pgs.entity.BankTransaction;
import com.pgs.entity.DepositRequest;
import com.pgs.entity.WithdrawalRequest;
import com.pgs.enums.DepositStatus;
import com.pgs.enums.WithdrawalStatus;
import com.pgs.repository.BankAccountRepository;
import com.pgs.repository.BankTransactionRepository;
import com.pgs.repository.DepositRequestRepository;
import com.pgs.repository.WithdrawalRequestRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
@RequiredArgsConstructor
@Slf4j
public class ReportingService {

    private final BankTransactionRepository bankTransactionRepository;
    private final BankAccountRepository bankAccountRepository;
    private final DepositRequestRepository depositRequestRepository;
    private final WithdrawalRequestRepository withdrawalRequestRepository;

    @Transactional(readOnly = true)
    public Page<Object> getTransactionReports(String search, LocalDate from, LocalDate to, Pageable pageable) {
        LocalDateTime fromDateTime = from != null ? from.atStartOfDay() : null;
        LocalDateTime toDateTime = to != null ? to.atTime(23, 59, 59) : null;
        
        return bankTransactionRepository.findWithFilters(search, fromDateTime, toDateTime, pageable)
            .map(this::mapTransactionToReport);

        //todo: add audit log who viewed the report
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> getBankBalances() {
        List<BankAccount> accounts = bankAccountRepository.findAllActive();
        List<Map<String, Object>> balances = new ArrayList<>();

        for (BankAccount account : accounts) {
            Map<String, Object> balance = new HashMap<>();
            balance.put("bankAccountId", account.getId().toString());
            balance.put("nickname", account.getNickname());
            balance.put("bankName", account.getBankName());
            balance.put("accountNumber", account.getAccountNumber());
            balance.put("balance", account.getCurrentBalance());
            balance.put("availableInLimit", account.getAvailableInLimit());
            balance.put("availableOutLimit", account.getAvailableOutLimit());
            //todo: add total_in and total_out, in limit, out limit, used_in, used_out, enable_in, enable_out
            balances.add(balance);
        }

        //todo: add audit log who viewed the report

        return balances;
    }

    @Transactional(readOnly = true)
    public List<Map<String, Object>> getSuccessRates(LocalDate from, LocalDate to) {
        List<Map<String, Object>> successRates = new ArrayList<>();
        
        LocalDate current = from;
        while (!current.isAfter(to)) {
            LocalDateTime dayStart = current.atStartOfDay();
            LocalDateTime dayEnd = current.atTime(23, 59, 59);

            // Count deposit requests
            long totalDeposits = depositRequestRepository.findWithFilters(null, dayStart, dayEnd, null, Pageable.unpaged()).getTotalElements();
            long successfulDeposits = depositRequestRepository.findWithFilters(null, dayStart, dayEnd, null, Pageable.unpaged())
                .stream()
                .mapToLong(dr -> DepositStatus.MATCHED.equals(dr.getStatus()) ? 1 : 0)
                .sum();

            // Count withdrawal requests
            long totalWithdrawals = withdrawalRequestRepository.findWithFilters(null, dayStart, dayEnd, null, Pageable.unpaged()).getTotalElements();
            long successfulWithdrawals = withdrawalRequestRepository.findWithFilters(null, dayStart, dayEnd, null, Pageable.unpaged())
                .stream()
                .mapToLong(wr -> WithdrawalStatus.SUCCESS.equals(wr.getStatus()) ? 1 : 0)
                .sum();

            Map<String, Object> dayRate = new HashMap<>();
            dayRate.put("date", current.toString());
            dayRate.put("depositSuccessRate", totalDeposits > 0 ? (double) successfulDeposits / totalDeposits : 0.0);
            dayRate.put("withdrawalSuccessRate", totalWithdrawals > 0 ? (double) successfulWithdrawals / totalWithdrawals : 0.0);
            dayRate.put("totalDeposits", totalDeposits);
            dayRate.put("successfulDeposits", successfulDeposits);
            dayRate.put("totalWithdrawals", totalWithdrawals);
            dayRate.put("successfulWithdrawals", successfulWithdrawals);
            
            successRates.add(dayRate);
            current = current.plusDays(1);
        }

        //todo: add audit log who viewed the report

        return successRates;
    }

    @Transactional(readOnly = true)
    public Page<Object> getDepositRequestReports(LocalDate from, LocalDate to, String bank, Pageable pageable) {
        LocalDateTime fromDateTime = from != null ? from.atStartOfDay() : null;
        LocalDateTime toDateTime = to != null ? to.atTime(23, 59, 59) : null;


        //todo: add audit log who viewed the report
        return depositRequestRepository.findWithFilters(null, fromDateTime, toDateTime, bank, pageable)
            .map(this::mapDepositRequestToReport);
    }

    @Transactional(readOnly = true)
    public Page<Object> getWithdrawalRequestReports(LocalDate from, LocalDate to, String bank, Pageable pageable) {
        LocalDateTime fromDateTime = from != null ? from.atStartOfDay() : null;
        LocalDateTime toDateTime = to != null ? to.atTime(23, 59, 59) : null;


        //todo: add audit log who viewed the report
        return withdrawalRequestRepository.findWithFilters(null, fromDateTime, toDateTime, bank, pageable)
            .map(this::mapWithdrawalRequestToReport);
    }

    private Object mapTransactionToReport(BankTransaction transaction) {
        return new Object() {
            public String getId() { return transaction.getId().toString(); }
            public String getBankName() { return transaction.getBankAccount().getBankName(); }
            public String getAccountNumber() { return transaction.getBankAccount().getAccountNumber(); }
            public String getDirection() { return transaction.getDirection().name(); }
            public String getAmount() { return transaction.getAmount().toString(); }
            public String getStatus() { return transaction.getStatus().name(); }
            public LocalDateTime getTransactionTime() { return transaction.getTransactionTime(); }
            public String getReference() { return transaction.getReference(); }
            public String getCounterpartyAccount() { return transaction.getCounterpartyAccount(); }
            public String getCounterpartyBank() { return transaction.getCounterpartyBank(); }
            public boolean isMatched() { return transaction.getMatchedRequestId() != null; }
        };
    }

    private Object mapDepositRequestToReport(DepositRequest request) {
        return new Object() {
            public String getId() { return request.getId().toString(); }
            public String getOrderNumber() { return request.getOrderNumber(); }
            public String getMerchantCode() { return request.getMerchantCode(); }
            public String getAmount() { return request.getAmount().toString(); }
            public String getCurrency() { return request.getCurrency(); }
            public String getStatus() { return request.getStatus().name(); }
            public String getBankName() { 
                return request.getAssignedBankAccount() != null ? 
                    request.getAssignedBankAccount().getBankName() : null; 
            }
            public LocalDateTime getCreatedAt() { return request.getCreatedAt(); }
            public LocalDateTime getExpiresAt() { return request.getExpiresAt(); }
            public boolean isMatched() { return request.getMatchedTransaction() != null; }
        };
    }

    private Object mapWithdrawalRequestToReport(WithdrawalRequest request) {
        return new Object() {
            public String getId() { return request.getId().toString(); }
            public String getOrderNumber() { return request.getOrderNumber(); }
            public String getMerchantCode() { return request.getMerchantCode(); }
            public String getAmount() { return request.getAmount().toString(); }
            public String getCurrency() { return request.getCurrency(); }
            public String getStatus() { return request.getStatus().name(); }
            public String getBankName() { return request.getBankAccount().getBankName(); }
            public String getTargetAccountNumber() { return request.getTargetAccountNumber(); }
            public String getTargetBankName() { return request.getTargetBankName(); }
            public LocalDateTime getCreatedAt() { return request.getCreatedAt(); }
            public LocalDateTime getExpiresAt() { return request.getExpiresAt(); }
            public boolean hasQrCode() { return request.getQrCode() != null; }
        };
    }
}
