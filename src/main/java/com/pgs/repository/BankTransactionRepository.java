package com.pgs.repository;

import com.pgs.entity.BankTransaction;
import com.pgs.enums.TransactionDirection;
import com.pgs.enums.TransactionStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

@Repository
public interface BankTransactionRepository extends JpaRepository<BankTransaction, UUID> {

    List<BankTransaction> findByBankAccountIdAndDirection(UUID bankAccountId, TransactionDirection direction);

    @Query("SELECT bt FROM BankTransaction bt WHERE bt.bankAccount.id = :bankAccountId " +
           "AND bt.direction = 'IN' AND bt.status = 'COMPLETED' " +
           "AND bt.matchedRequestId IS NULL " +
           "AND bt.transactionTime >= :fromTime " +
           "ORDER BY bt.transactionTime ASC")
    List<BankTransaction> findUnmatchedIncomingTransactions(@Param("bankAccountId") UUID bankAccountId,
                                                           @Param("fromTime") LocalDateTime fromTime);

    //todo: status should be UNMATCHED
    @Query("SELECT bt FROM BankTransaction bt WHERE bt.matchedRequestId IS NULL " +
           "AND bt.direction = 'IN' AND bt.status = 'COMPLETED' " +
           "AND (:bankName IS NULL OR bt.bankAccount.bankName = :bankName) " +
           "AND (:date IS NULL OR DATE(bt.transactionTime) = DATE(:date))")
    Page<BankTransaction> findUnmatchedTransactions(@Param("bankName") String bankName,
                                                   @Param("date") LocalDateTime date,
                                                   Pageable pageable);

    @Query("SELECT bt FROM BankTransaction bt WHERE " +
           "(:search IS NULL OR bt.reference LIKE %:search%) " +
           "AND (:fromDate IS NULL OR bt.transactionTime >= :fromDate) " +
           "AND (:toDate IS NULL OR bt.transactionTime <= :toDate)")
    Page<BankTransaction> findWithFilters(@Param("search") String search,
                                        @Param("fromDate") LocalDateTime fromDate,
                                        @Param("toDate") LocalDateTime toDate,
                                        Pageable pageable);

    @Query("SELECT SUM(bt.amount) FROM BankTransaction bt WHERE bt.bankAccount.id = :bankAccountId " +
           "AND bt.direction = :direction AND bt.status = 'COMPLETED' " +
           "AND DATE(bt.transactionTime) = CURRENT_DATE")
    BigDecimal getDailyTransactionSum(@Param("bankAccountId") UUID bankAccountId,
                                    @Param("direction") TransactionDirection direction);
}
