package com.pgs.entity;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;

import static org.junit.jupiter.api.Assertions.*;

class BankAccountTest {

    private BankAccount bankAccount;

    @BeforeEach
    void setUp() {
        bankAccount = BankAccount.builder()
            .nickname("Test Account")
            .accountNumber("**********")
            .bankName("Test Bank")
            .currentBalance(new BigDecimal("1000.00"))
            .dailyInUsed(new BigDecimal("200.00"))
            .dailyOutUsed(new BigDecimal("150.00"))
            .freezedDailyInUsed(new BigDecimal("100.00"))
            .freezedDailyOutUsed(new BigDecimal("50.00"))
            .totalIn(new BigDecimal("5000.00"))
            .totalOut(new BigDecimal("3000.00"))
            .build();
    }

    @Test
    void testFreezeDepositAmount() {
        // Arrange
        BigDecimal freezeAmount = new BigDecimal("75.00");
        BigDecimal initialFrozenDailyInUsed = bankAccount.getFreezedDailyInUsed();

        // Act
        bankAccount.freezeDepositAmount(freezeAmount);

        // Assert
        assertEquals(initialFrozenDailyInUsed.add(freezeAmount), bankAccount.getFreezedDailyInUsed());
    }

    @Test
    void testFreezeWithdrawalAmount() {
        // Arrange
        BigDecimal freezeAmount = new BigDecimal("25.00");
        BigDecimal initialFrozenDailyOutUsed = bankAccount.getFreezedDailyOutUsed();

        // Act
        bankAccount.freezeWithdrawalAmount(freezeAmount);

        // Assert
        assertEquals(initialFrozenDailyOutUsed.add(freezeAmount), bankAccount.getFreezedDailyOutUsed());
    }

    @Test
    void testReleaseFrozenDepositAmount() {
        // Arrange
        BigDecimal releaseAmount = new BigDecimal("30.00");
        BigDecimal initialFrozenDailyInUsed = bankAccount.getFreezedDailyInUsed();

        // Act
        bankAccount.releaseFrozenDepositAmount(releaseAmount);

        // Assert
        assertEquals(initialFrozenDailyInUsed.subtract(releaseAmount), bankAccount.getFreezedDailyInUsed());
    }

    @Test
    void testReleaseFrozenWithdrawalAmount() {
        // Arrange
        BigDecimal releaseAmount = new BigDecimal("20.00");
        BigDecimal initialFrozenDailyOutUsed = bankAccount.getFreezedDailyOutUsed();

        // Act
        bankAccount.releaseFrozenWithdrawalAmount(releaseAmount);

        // Assert
        assertEquals(initialFrozenDailyOutUsed.subtract(releaseAmount), bankAccount.getFreezedDailyOutUsed());
    }

    @Test
    void testCanAcceptDeposit() {
        // Arrange
        bankAccount.setIsActive(true);
        bankAccount.setEnableIn(true);
        bankAccount.setInTransferLimit(new BigDecimal("500.00"));
        bankAccount.setBalanceLimit(new BigDecimal("2000.00"));

        // Act & Assert
        assertTrue(bankAccount.canAcceptDeposit(new BigDecimal("100.00"))); // Within limits
        assertFalse(bankAccount.canAcceptDeposit(new BigDecimal("300.00"))); // Exceeds available in limit
        assertFalse(bankAccount.canAcceptDeposit(new BigDecimal("1200.00"))); // Would exceed balance limit
    }

    @Test
    void testCanProcessWithdrawal() {
        // Arrange
        bankAccount.setIsActive(true);
        bankAccount.setEnableOut(true);
        bankAccount.setOutTransferLimit(new BigDecimal("300.00"));

        // Act & Assert
        assertTrue(bankAccount.canProcessWithdrawal(new BigDecimal("50.00"))); // Within limits
        assertFalse(bankAccount.canProcessWithdrawal(new BigDecimal("150.00"))); // Exceeds available out limit
        assertFalse(bankAccount.canProcessWithdrawal(new BigDecimal("1200.00"))); // Exceeds balance
    }

    @Test
    void testGetAvailableInLimit() {
        // Arrange
        bankAccount.setInTransferLimit(new BigDecimal("500.00"));

        // Act
        BigDecimal availableInLimit = bankAccount.getAvailableInLimit();

        // Assert
        BigDecimal expected = new BigDecimal("500.00")
            .subtract(bankAccount.getDailyInUsed())
            .subtract(bankAccount.getFreezedDailyInUsed());
        assertEquals(expected, availableInLimit);
    }

    @Test
    void testGetAvailableOutLimit() {
        // Arrange
        bankAccount.setOutTransferLimit(new BigDecimal("300.00"));

        // Act
        BigDecimal availableOutLimit = bankAccount.getAvailableOutLimit();

        // Assert
        BigDecimal expected = new BigDecimal("300.00")
            .subtract(bankAccount.getDailyOutUsed())
            .subtract(bankAccount.getFreezedDailyOutUsed());
        assertEquals(expected, availableOutLimit);
    }

    @Test
    void testResetDailyLimits() {
        // Act
        bankAccount.resetDailyLimits();

        // Assert
        assertEquals(BigDecimal.ZERO, bankAccount.getDailyInUsed());
        assertEquals(BigDecimal.ZERO, bankAccount.getDailyOutUsed());
        assertEquals(BigDecimal.ZERO, bankAccount.getFreezedDailyInUsed());
        assertEquals(BigDecimal.ZERO, bankAccount.getFreezedDailyOutUsed());
    }
}
